#!/usr/bin/env python3
"""
测试文件保存诊断功能
"""

import sys
import os
import pandas as pd
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_file_save_diagnostics():
    """
    测试文件保存的各种情况
    """
    print("🧪 Testing file save diagnostics...")
    
    # 创建临时测试目录
    test_dir = tempfile.mkdtemp(prefix="futu_test_")
    print(f"Created test directory: {test_dir}")
    
    try:
        # 测试1: 正常保存
        print("\n📝 Test 1: Normal save")
        test_data = pd.DataFrame({
            'time_key': ['2024-01-01 00:00:00', '2024-01-02 00:00:00'],
            'close': [100.0, 101.0],
            'volume': [1000, 1100]
        })
        
        test_file = os.path.join(test_dir, "test_normal.csv")
        print(f"Saving to: {test_file}")
        print(f"Directory exists: {os.path.exists(test_dir)}")
        print(f"Directory writable: {os.access(test_dir, os.W_OK)}")
        
        test_data.to_csv(test_file, index=False)
        
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"✅ File saved successfully, size: {file_size} bytes")
        else:
            print(f"❌ File was not created")
        
        # 测试2: 空数据
        print("\n📝 Test 2: Empty data")
        empty_data = pd.DataFrame()
        test_file_empty = os.path.join(test_dir, "test_empty.csv")
        
        if empty_data.empty:
            print("❌ Data is empty, cannot save")
        else:
            empty_data.to_csv(test_file_empty, index=False)
        
        # 测试3: None数据
        print("\n📝 Test 3: None data")
        none_data = None
        
        if none_data is None:
            print("❌ Data is None, cannot save")
        
        # 测试4: 权限问题（如果可能的话）
        print("\n📝 Test 4: Permission test")
        readonly_dir = os.path.join(test_dir, "readonly")
        os.makedirs(readonly_dir, exist_ok=True)
        
        try:
            # 尝试使权限只读（在某些系统上可能不起作用）
            os.chmod(readonly_dir, 0o444)
            readonly_file = os.path.join(readonly_dir, "test_readonly.csv")
            
            try:
                test_data.to_csv(readonly_file, index=False)
                print("✅ File saved despite readonly directory")
            except Exception as e:
                print(f"❌ Permission error as expected: {e}")
                
        except Exception as e:
            print(f"Cannot test permissions: {e}")
        
    finally:
        # 清理
        shutil.rmtree(test_dir)
        print(f"\n🧹 Cleaned up test directory: {test_dir}")

def test_path_diagnostics():
    """
    测试路径相关的诊断
    """
    print("\n🔍 Testing path diagnostics...")
    
    # 测试各种路径情况
    test_paths = [
        "/tmp/futu_test",  # 正常路径
        "/nonexistent/path/futu_test",  # 不存在的路径
        "",  # 空路径
        "relative/path/futu_test"  # 相对路径
    ]
    
    for path in test_paths:
        print(f"\n📁 Testing path: '{path}'")
        
        if not path:
            print("❌ Empty path")
            continue
            
        print(f"Path exists: {os.path.exists(path)}")
        
        if os.path.exists(path):
            print(f"Is directory: {os.path.isdir(path)}")
            print(f"Is writable: {os.access(path, os.W_OK)}")
        else:
            parent_dir = os.path.dirname(path)
            if parent_dir:
                print(f"Parent directory exists: {os.path.exists(parent_dir)}")
                if os.path.exists(parent_dir):
                    print(f"Parent directory writable: {os.access(parent_dir, os.W_OK)}")

if __name__ == "__main__":
    test_file_save_diagnostics()
    test_path_diagnostics()
