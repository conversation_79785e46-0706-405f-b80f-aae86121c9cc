#!/usr/bin/env python3
"""
测试实际的股票数据下载功能
"""

import sys
import os
import pandas as pd
import tempfile
import shutil
from futu import *
import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的函数
from data.get_daily_data_final import process_single_stock_final
from utilities.utils import get_last_futu_trading_day

def test_actual_download():
    """
    测试实际的股票数据下载
    """
    print("🧪 Testing actual stock data download...")
    
    # 创建临时测试目录
    test_dir = tempfile.mkdtemp(prefix="futu_download_test_")
    print(f"Created test directory: {test_dir}")
    
    # 测试股票代码 - 选择一个真实存在的股票
    test_stock = "00700"  # 腾讯控股
    
    quote_ctx = None
    try:
        print("🔌 Connecting to Futu API...")
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        print("✅ Connected to Futu API")
        
        # 获取最近的交易日
        last_trading_day = get_last_futu_trading_day(quote_ctx, 'HK', 30)
        print(f"📈 Last trading day: {last_trading_day}")
        
        print(f"\n📊 Testing download for stock: {test_stock}")
        
        # 确保测试文件不存在，强制全量下载
        test_file = os.path.join(test_dir, f"{test_stock}.csv")
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"Removed existing test file: {test_file}")
        
        # 执行下载测试
        print(f"\n🚀 Starting download test...")
        result = process_single_stock_final(quote_ctx, test_stock, test_dir, last_trading_day)
        
        print(f"\n📋 Download result: {result}")
        
        # 验证结果
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"✅ File created successfully!")
            print(f"   File path: {test_file}")
            print(f"   File size: {file_size} bytes")
            
            # 读取并验证数据
            try:
                df = pd.read_csv(test_file)
                print(f"   Records count: {len(df)}")
                print(f"   Columns: {list(df.columns)}")
                
                if not df.empty:
                    print(f"   Date range: {df['time_key'].min()} to {df['time_key'].max()}")
                    print(f"   Sample data:")
                    print(df.head(2).to_string(index=False))
                else:
                    print("   ❌ File is empty!")
                    
            except Exception as e:
                print(f"   ❌ Error reading file: {e}")
        else:
            print(f"❌ File was not created at: {test_file}")
            print(f"   Directory contents: {os.listdir(test_dir)}")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if quote_ctx:
            quote_ctx.close()
            print("🔌 Disconnected from Futu API")
        
        # 清理测试目录
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"🧹 Cleaned up test directory: {test_dir}")

if __name__ == "__main__":
    test_actual_download()
