#!/usr/bin/env python3
"""
测试下载诊断功能
"""

import sys
import os
import pandas as pd
from futu import *
import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的函数
from data.get_daily_data_final import process_single_stock_final, get_incremental_history_kline
from utilities.utils import get_last_futu_trading_day

def test_download_diagnostics():
    """
    测试下载诊断功能
    """
    print("🧪 Testing download diagnostics...")
    
    # 创建测试目录
    test_dir = "test_output"
    os.makedirs(test_dir, exist_ok=True)
    
    # 测试股票代码
    test_codes = ['06088', '99999']  # 06088是真实股票，99999可能不存在
    
    quote_ctx = None
    try:
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        print("✅ Connected to Futu API")
        
        # 获取最近的交易日
        last_trading_day = get_last_futu_trading_day(quote_ctx, 'HK', 30)
        print(f"📈 Last trading day: {last_trading_day}")
        
        for stock_code in test_codes:
            print(f"\n📊 Testing stock code: {stock_code}")
            
            # 删除现有文件以强制全量下载
            test_file = os.path.join(test_dir, f"{stock_code}.csv")
            if os.path.exists(test_file):
                os.remove(test_file)
                print(f"Removed existing file: {test_file}")
            
            # 测试下载
            result = process_single_stock_final(quote_ctx, stock_code, test_dir, last_trading_day)
            print(f"Result: {result}")
            
            # 检查文件是否创建
            if os.path.exists(test_file):
                df = pd.read_csv(test_file)
                print(f"✅ File created with {len(df)} records")
            else:
                print(f"❌ File not created")
                
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if quote_ctx:
            quote_ctx.close()
            print("🔌 Disconnected from Futu API")
    
    # 清理测试文件
    import shutil
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
        print(f"🧹 Cleaned up test directory: {test_dir}")

def test_api_directly():
    """
    直接测试API调用
    """
    print("\n🔧 Testing API directly...")
    
    quote_ctx = None
    try:
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        print("✅ Connected to Futu API")
        
        # 测试有效和无效的股票代码
        test_codes = ['HK.06088', 'HK.99999']
        
        for code in test_codes:
            print(f"\n📊 Testing direct API call for: {code}")
            
            result = get_incremental_history_kline(quote_ctx, code, '2024-01-01')
            
            if result is not None and not result.empty:
                print(f"✅ {code}: Retrieved {len(result)} records")
                print(f"Date range: {result['time_key'].min()} to {result['time_key'].max()}")
            else:
                print(f"❌ {code}: No data retrieved")
                
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if quote_ctx:
            quote_ctx.close()

if __name__ == "__main__":
    test_download_diagnostics()
    test_api_directly()
