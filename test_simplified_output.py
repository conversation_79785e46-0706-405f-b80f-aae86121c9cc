#!/usr/bin/env python3
"""
测试简化后的输出
"""

import sys
import os
import pandas as pd
import tempfile
import shutil
from futu import *
import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的函数
from data.get_daily_data_final import process_single_stock_final
from utilities.utils import get_last_futu_trading_day

def test_simplified_output():
    """
    测试简化后的输出
    """
    print("🧪 Testing simplified output...")
    
    # 创建临时测试目录
    test_dir = tempfile.mkdtemp(prefix="futu_simple_test_")
    print(f"Created test directory: {test_dir}")
    
    # 测试股票代码
    test_stock = "00700"  # 腾讯控股
    
    quote_ctx = None
    try:
        print("🔌 Connecting to Futu API...")
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        print("✅ Connected to Futu API")
        
        # 获取最近的交易日
        last_trading_day = get_last_futu_trading_day(quote_ctx, 'HK', 30)
        print(f"📈 Last trading day: {last_trading_day}")
        
        print(f"\n📊 Testing simplified output for stock: {test_stock}")
        
        # 确保测试文件不存在，强制全量下载
        test_file = os.path.join(test_dir, f"{test_stock}.csv")
        if os.path.exists(test_file):
            os.remove(test_file)
        
        # 执行下载测试
        print(f"\n🚀 Starting download test...")
        result = process_single_stock_final(quote_ctx, test_stock, test_dir, last_trading_day)
        
        print(f"\n📋 Result: {result}")
        
        # 验证结果
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"✅ File created: {file_size} bytes")
        else:
            print(f"❌ File was not created")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        
    finally:
        if quote_ctx:
            quote_ctx.close()
            print("🔌 Disconnected from Futu API")
        
        # 清理测试目录
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"🧹 Cleaned up test directory")

if __name__ == "__main__":
    test_simplified_output()
